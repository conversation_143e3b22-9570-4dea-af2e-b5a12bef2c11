<script lang="ts">
	// Svelte imports
	import { createEventDispatcher, onMount, onDestroy } from 'svelte';

	// UI component imports
	import { <PERSON><PERSON>, Button } from 'flowbite-svelte';
	import { CloseOutline, DownloadOutline, EnvelopeOutline, PhoneOutline } from 'flowbite-svelte-icons';

	// Store imports
	import { t } from '$lib/stores/i18n';
	import { toastStore } from '$lib/stores/toastStore';
	import { getBackendUrl } from '$lib/config';

	// API imports
	import { CustomerService } from '$lib/api/features/customer/customers.service';

	// Component imports
	import LoadingSpinner from '$lib/components/common/LoadingSpinner.svelte';
	import Profile from '$lib/components/customer/detail/profile.svelte';
	import Ticket from '$lib/components/customer/detail/ticket.svelte';
	// import Policy from '$lib/components/customer/detail/policy.svelte'; // For further development
	import Notes from '$lib/components/customer/detail/notes.svelte';
	import Memory from '$lib/components/customer/detail/memory.svelte';

	// Constants
	const TABS = [
		{ id: 'customer-info', label: 'Customer Info', key: 'customer_sidebar_tab_overview' },
		{ id: 'tickets', label: 'Tickets', key: 'customer_sidebar_tab_tickets' },
		// { id: 'policies', label: 'Insurance Policies', key: 'policies' }, // For further development
		{ id: 'notes-memories', label: 'Notes & Memories', key: 'customer_sidebar_tab_notes_memories' }
	];

	// Props
	export let isOpen = false;
	export let selectedCustomerId: number | null = null;
	export let access_token: string;

	// Event dispatcher
	const dispatch = createEventDispatcher();

	// Component state
	let isLoading = false;
	let error: string | null = null;
	let customer: any = null;
	let customerNotes: any[] = [];
	let customerPolicies: any = null;
	let customerTickets: any = null;
	let customerTags: any[] = [];
	let customerMemory: any[] = [];
	let isExportingConversation = false;

	// Tab state
	let activeTab = 'customer-info';

	// Customer service instance
	const customerService = new CustomerService();

	// Reactive data with proper display logic
	$: displayCustomer = customer;
	$: hasCustomerImage =
		displayCustomer && displayCustomer.picture_url && displayCustomer.picture_url.length > 0;

	// Watch for selectedCustomerId changes
	$: if (selectedCustomerId && isOpen) {
		fetchCustomerDetails();
	}

	// Focus management
	$: if (isOpen) {
		setTimeout(() => {
			const closeButton = document.getElementById('customer-detail-sidebar-close-button');
			if (closeButton) {
				closeButton.focus();
			}
		}, 100);
	}

	// Functions
	function closeSidebar() {
		dispatch('closeSidebar');
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape') {
			closeSidebar();
		} else if (event.key === 'Tab' && isOpen) {
			// Trap focus within sidebar when open
			const sidebar = document.getElementById('customer-detail-sidebar-container');
			if (sidebar) {
				const focusableElements = sidebar.querySelectorAll(
					'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
				);
				const firstElement = focusableElements[0] as HTMLElement;
				const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

				if (event.shiftKey && document.activeElement === firstElement) {
					event.preventDefault();
					lastElement?.focus();
				} else if (!event.shiftKey && document.activeElement === lastElement) {
					event.preventDefault();
					firstElement?.focus();
				}
			}
		}
	}

	async function fetchCustomerDetails() {
		if (!selectedCustomerId || !access_token) return;

		isLoading = true;
		error = null;

		try {
			// Parallel API calls for efficiency
			const [customerResponse, notesResponse, policiesResponse, ticketsResponse, memoryResponse] =
				await Promise.all([
					customerService.getById(selectedCustomerId.toString(), access_token),
					customerService.getCustomerNotes(selectedCustomerId.toString(), access_token),
					customerService.getCustomerOwnPolicies(selectedCustomerId.toString(), access_token),
					customerService.getCustomerTickets(selectedCustomerId.toString(), access_token),
					customerService.getMemoryById(selectedCustomerId.toString(), access_token)
				]);

			// Process customer response
			if (customerResponse.res_status === 200) {
				customer = customerResponse.customers;
				// Extract tags from customer data if available
				customerTags = customer?.tags || [];
			} else {
				throw new Error(customerResponse.error_msg || 'Failed to fetch customer details');
			}

			// Process notes response
			if (notesResponse.res_status === 200) {
				customerNotes = Array.isArray(notesResponse.customer_notes)
					? notesResponse.customer_notes
					: [];
			}

			// Process policies response
			if (policiesResponse.res_status === 200) {
				customerPolicies = policiesResponse.customer_policies;
			}

			// Process tickets response
			if (ticketsResponse.res_status === 200) {
				customerTickets = ticketsResponse.customer_tickets;
			}

			// Process memory response
			if (memoryResponse.res_status === 200) {
				customerMemory = Array.isArray(memoryResponse.memories) ? memoryResponse.memories : [];
			}
		} catch (err) {
			console.error('Error fetching customer details:', err);
			error = err instanceof Error ? err.message : 'Failed to fetch customer details';
		} finally {
			isLoading = false;
		}
	}

	// Unified refresh function for better maintainability
	async function refreshCustomerData() {
		if (!selectedCustomerId || !access_token) return;

		try {
			// Refresh customer data without showing main loading state
			const [customerResponse, notesResponse, policiesResponse, ticketsResponse, memoryResponse] =
				await Promise.all([
					customerService.getById(selectedCustomerId.toString(), access_token),
					customerService.getCustomerNotes(selectedCustomerId.toString(), access_token),
					customerService.getCustomerOwnPolicies(selectedCustomerId.toString(), access_token),
					customerService.getCustomerTickets(selectedCustomerId.toString(), access_token),
					customerService.getMemoryById(selectedCustomerId.toString(), access_token)
				]);

			// Update data silently
			if (customerResponse.res_status === 200) {
				customer = customerResponse.customers;
				customerTags = customer?.tags || [];
			}

			if (notesResponse.res_status === 200) {
				customerNotes = Array.isArray(notesResponse.customer_notes)
					? notesResponse.customer_notes
					: [];
			}

			if (policiesResponse.res_status === 200) {
				customerPolicies = policiesResponse.customer_policies;
			}

			if (ticketsResponse.res_status === 200) {
				customerTickets = ticketsResponse.customer_tickets;
			}

			if (memoryResponse.res_status === 200) {
				customerMemory = Array.isArray(memoryResponse.memories) ? memoryResponse.memories : [];
			}
		} catch (err) {
			console.error('Error refreshing customer data:', err);
			// Don't show error for background refresh, just log it
		}
	}

	// Export functionality
	async function exportCustomerChat() {
		if (!displayCustomer) return;

		isExportingConversation = true;

		const url = `${getBackendUrl()}/export/customers/${customer.customer_id}/export-conversations-direct/`;
		const bodyData = {
			format: 'excel',
			include_deleted_messages: true,
			include_attachments: true,
			store_permanently: false
		};

		fetch(url, {
			method: 'POST',
			headers: {
				Authorization: `Bearer ${access_token}`,
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(bodyData)
		})
			.then((response) => response.blob())
			.then((blob) => {
				const link = document.createElement('a');
				link.href = URL.createObjectURL(blob);
				link.click();
				toastStore.add(t('export_customer_conversations_success'), 'success');
				isExportingConversation = false;
			})
			.catch((error) => {
				console.error('Error downloading the file:', error);
				toastStore.add(t('export_customer_conversations_failed'), 'error');
				isExportingConversation = false;
			});
	}

	// Keyboard event listener
	onMount(() => {
		document.addEventListener('keydown', handleKeydown);
		return () => {
			document.removeEventListener('keydown', handleKeydown);
		};
	});

	// Cleanup on destroy
	onDestroy(() => {
		if (typeof document !== 'undefined') {
			document.removeEventListener('keydown', handleKeydown);
		}
	});
</script>

<svelte:window on:keydown={handleKeydown} />

<!-- Sidebar Container -->
<div
	id="customer-detail-sidebar-container"
	class="fixed inset-y-0 right-0 z-50 w-full transform bg-white shadow-xl transition-transform duration-300 ease-in-out sm:w-4/5 md:w-3/5 lg:w-1/2 xl:w-2/5 {isOpen
		? 'translate-x-0'
		: 'translate-x-full'}"
	role="dialog"
	aria-modal="true"
	aria-labelledby="customer-detail-sidebar-title"
	aria-describedby="customer-detail-sidebar-description"
>
	<!-- Header -->
	<div
		id="customer-detail-sidebar-header"
		class="flex items-center justify-between border-b border-gray-200 p-4"
	>
		<div>
			<h2 id="customer-detail-sidebar-title" class="text-md text-gray-900">
				{t('customer_details')}
			</h2>
			<p id="customer-detail-sidebar-description" class="sr-only text-sm text-gray-500">
				{t('customer_details_description')}
			</p>
		</div>
		<Button
			id="customer-detail-sidebar-close-button"
			color="none"
			size="sm"
			class="p-2 hover:bg-gray-100"
			on:click={closeSidebar}
			aria-label={t('close')}
		>
			<CloseOutline class="h-5 w-5" />
		</Button>
	</div>

	<!-- Content -->
	<div id="customer-detail-sidebar-content" class="flex flex-1 flex-col">
		{#if isLoading}
			<div id="customer-detail-sidebar-loading" class="flex h-64 items-center justify-center">
				<LoadingSpinner size="lg" />
			</div>
		{:else if error}
			<div id="customer-detail-sidebar-error" class="m-4 rounded-lg bg-red-50 p-4">
				<p class="text-sm text-red-600">{error}</p>
				<Button
					id="customer-detail-sidebar-retry-button"
					color="red"
					size="sm"
					class="mt-2"
					on:click={fetchCustomerDetails}
				>
					{t('retry')}
				</Button>
			</div>
		{:else if displayCustomer}
			<!-- Customer Header Section -->
			<div
				id="customer-detail-sidebar-customer-header"
				class="flex-shrink-0 border-b border-gray-200 p-4"
			>
				<div class="flex items-start">
					<div class="relative mr-4">
						<div class="relative h-16 w-16 overflow-hidden rounded-full bg-gray-100">
							{#if hasCustomerImage}
								<img
									src={displayCustomer.picture_url}
									alt="{displayCustomer.first_name} {displayCustomer.last_name}"
									class="h-full w-full object-cover"
								/>
							{:else}
								<img
									src="/images/person-logo.png"
									alt="Default profile"
									class="h-full w-full object-cover"
								/>
							{/if}
						</div>
					</div>
					<div class="flex-1 min-w-0">
						<h3
							id="customer-detail-sidebar-customer-name"
							class="truncate text-lg font-bold text-gray-900"
							title="{displayCustomer.first_name && displayCustomer.last_name
								? `${displayCustomer.first_name} ${displayCustomer.last_name}`
								: displayCustomer.name || 'Unknown Customer'}"
						>
							{displayCustomer.first_name && displayCustomer.last_name
								? `${displayCustomer.first_name} ${displayCustomer.last_name}`
								: displayCustomer.name || 'Unknown Customer'}
						</h3>
						<div class="mt-2 flex items-center gap-2">
							{#if displayCustomer.email}
								<Badge color="blue"><EnvelopeOutline class="mr-1 h-4 w-4" />{displayCustomer.email}</Badge>
							{/if}
							{#if displayCustomer.phone}
								<Badge color="green"><PhoneOutline class="mr-1 h-4 w-4" />{displayCustomer.phone}</Badge>
							{/if}
						</div>
					</div>
				</div>
			</div>

			<!-- Tab Navigation -->
			<div
				id="customer-detail-sidebar-tabs-container"
				class="flex-shrink-0 border-b border-gray-200 bg-white"
			>
				<nav id="customer-detail-sidebar-tabs" class="flex w-full">
					{#each TABS as tab}
						<button
							id="customer-detail-sidebar-tab-{tab.id}"
							on:click={() => {
								activeTab = tab.id;
							}}
							on:keydown={(e) => {
								if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
									e.preventDefault();
									const currentIndex = TABS.findIndex((t) => t.id === tab.id);
									let nextIndex;
									if (e.key === 'ArrowLeft') {
										nextIndex = currentIndex > 0 ? currentIndex - 1 : TABS.length - 1;
									} else {
										nextIndex = currentIndex < TABS.length - 1 ? currentIndex + 1 : 0;
									}
									const nextTab = TABS[nextIndex];
									activeTab = nextTab.id;
									document.getElementById(`customer-detail-sidebar-tab-${nextTab.id}`)?.focus();
								}
							}}
							class="flex-1 whitespace-nowrap border-b-2 px-4 py-4 text-center text-sm font-medium transition-colors
							{activeTab === tab.id
								? 'border-black bg-white text-black'
								: 'border-transparent text-gray-500 hover:text-gray-700'}"
							role="tab"
							aria-selected={activeTab === tab.id}
							aria-controls="customer-detail-sidebar-tab-content-{tab.id}"
							tabindex={activeTab === tab.id ? 0 : -1}
						>
							{t(tab.key)}
							{#if tab.id === 'tickets' && customerTickets && customerTickets.tickets.length > 0}
								<span
									class="ml-1 rounded-full bg-gray-200 px-2 py-1 text-sm font-medium text-gray-600"
								>
									{customerTickets.tickets.length}
								</span>
							{/if}
						</button>
					{/each}
				</nav>
			</div>

			<!-- Tab Content -->
			<div id="customer-detail-sidebar-tab-content" class="w-full flex-1 bg-white">
				<div class="h-full w-full">
					{#each TABS as tab}
						{#if activeTab === tab.id}
							<div
								id="customer-detail-sidebar-tab-content-{tab.id}"
								role="tabpanel"
								aria-labelledby="customer-detail-sidebar-tab-{tab.id}"
								class="max-h-[calc(100vh-14rem)] overflow-y-auto p-4"
							>
								{#if tab.id === 'customer-info'}
									<!-- Customer Info Tab -->
									<div id="customer-detail-sidebar-customer-info-section">
										<!-- Customer Profile -->
										<div class="mb-6">
											<Profile
												customer={displayCustomer}
												access_token={access_token}
											/>
										</div>

										<!-- Export Buttons -->
										<div class="flex flex-col space-y-3">
											<Button
												id="customer-detail-sidebar-export-chat-button"
												type="button"
												color="blue"
												disabled={isExportingConversation}
												on:click={exportCustomerChat}
											>
												{#if isExportingConversation}
													<div class="ml-2 h-4 w-4 animate-spin rounded-full border-b-2 border-blue-500"></div>
												{:else}
													<DownloadOutline class="mr-2 h-4 w-4" />
													{t('export_customer_conversations')}
												{/if}
											</Button>
											<!-- For further development -->
											<!-- <Button
												id="customer-detail-sidebar-export-notes-button"
												type="button"
												color="blue"
												disabled
											>
												<DownloadOutline class="mr-2 h-4 w-4" />
												{t('export_customer_notes')}
											</Button> -->
										</div>
									</div>
								{:else if tab.id === 'tickets'}
									<!-- Tickets Tab -->
									<div id="customer-detail-sidebar-tickets-section">
										{#if customerTickets}
											<Ticket customer_tickets={customerTickets} />
										{:else}
											<div class="flex h-32 items-center justify-center text-gray-500">
												<p>{t('no_tickets_found')}</p>
											</div>
										{/if}
									</div>
								<!-- For further development -->
								<!-- Policies Tab -->
								<!-- {:else if tab.id === 'policies'}
									<div id="customer-detail-sidebar-policies-section">
										{#if customerPolicies}
											<Policy customer_policies={customerPolicies} />
										{:else}
											<div class="flex h-32 items-center justify-center text-gray-500">
												<p>{t('no_policies_found')}</p>
											</div>
										{/if}
									</div> -->
								{:else if tab.id === 'notes-memories'}
									<!-- Notes & Memories Tab -->
									<div id="customer-detail-sidebar-notes-memories-section">
										<!-- Notes Section -->
										<div class="mb-8">
											<Notes
												customer={displayCustomer}
												customer_notes={customerNotes}
												access_token={access_token}
											/>
										</div>

										<!-- Memories Section -->
										<div>
											<Memory memories={customerMemory} />
										</div>
									</div>
								{/if}
							</div>
						{/if}
					{/each}
				</div>
			</div>
		{/if}
	</div>
</div>

<style>
	/* Custom scrollbar styling for tab content */
	.max-h-\[calc\(100vh-14rem\)\]::-webkit-scrollbar {
		width: 8px;
	}

	.max-h-\[calc\(100vh-14rem\)\]::-webkit-scrollbar-track {
		background: #f1f1f1;
		border-radius: 3px;
	}

	.max-h-\[calc\(100vh-14rem\)\]::-webkit-scrollbar-thumb {
		background: #c1c1c1;
		border-radius: 3px;
	}

	.max-h-\[calc\(100vh-14rem\)\]::-webkit-scrollbar-thumb:hover {
		background: #a1a1a1;
	}

	/* Smooth scrolling */
	.max-h-\[calc\(100vh-14rem\)\] {
		scroll-behavior: smooth;
	}
</style>
