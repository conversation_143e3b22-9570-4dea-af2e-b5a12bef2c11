<!-- profiles.svelte -->
<script lang="ts">
	import { t, language } from '$src/lib/stores/i18n';
	import { get } from 'svelte/store';

	export let customer: any;
	export let access_token: string;
	
	import { Indicator } from 'flowbite-svelte';
	import { UserCircleSolid, TagSolid } from 'flowbite-svelte-icons';
	import {
		getColorClass,
		formatDateOfBirth,
		getFullNationality,
		formatFullAddress,
		hasValidAddress
	} from '$lib/utils';
	import CustomerEdit from '$lib/components/UI/CustomerEdit.svelte';
	import CustomerTag from '$src/lib/components/UI/CustomerTag.svelte';
	import { services } from '$src/lib/api/features';
	import { getBackendUrl } from '$src/lib/config';
	import { triggerRefresh } from '$src/lib/stores/refreshStore';

	$: lang = get(language);

	// State management for customer tags
	let customerTags: any[] = [];
	let loadingTags = false;
	let refreshingTags = false;

	// Load available customer tags for the modal
	async function loadCustomerTags() {
		try {
			loadingTags = true;
			// Fetch all available tags for the tag selection modal
			const response_customer_tag = await services.customers.getFilterTags(access_token);
			customerTags = response_customer_tag.data || [];
		} catch (error) {
			console.error('Error loading customer tags:', error);
			customerTags = [];
		} finally {
			loadingTags = false;
		}
	}

	// Refresh customer data including tags
	async function refreshCustomerTags() {
		try {
			refreshingTags = true;

			// Fetch fresh customer data to get updated tags
			const customerResponse = await fetch(`${getBackendUrl()}/customer/api/customers/${customer.customer_id}/`, {
				credentials: 'include'
			});

			if (customerResponse.ok) {
				const freshCustomerData = await customerResponse.json();
				console.log('Profile.svelte: refreshCustomerTags(): Successfully fetched customer tags:', freshCustomerData);

				// Update the customer object with fresh data (including tags)
				customer = freshCustomerData;
			} else {
				const errorText = await customerResponse.text();
				throw new Error(`Profile.svelte: refreshCustomerTags(): Failed to fetch customer tags: ${customerResponse.status} - ${errorText}`);
			}

			// Also refresh the available tags for the modal
			await loadCustomerTags();

		} catch (error) {
			console.error('Profile.svelte: refreshCustomerTags(): Failed to refresh customer tags:', error);
		} finally {
			refreshingTags = false;
		}
	}

	// Comprehensive refresh function for customer data after edit operations
	// Following the loadCustomerDetails pattern from chat_center/+page.svelte
	async function refreshCustomerData() {
		try {

			// Fetch fresh customer data
			const response = await fetch(`${getBackendUrl()}/customer/api/customers/${customer.customer_id}/`, {
				credentials: 'include'
			});

			if (response.ok) {
				const freshCustomerData = await response.json();

				// Update the customer object with all fresh data
				customer = freshCustomerData;
			} else {
				const errorText = await response.text();
				console.error(`Profile.svelte: refreshCustomerData(): Failed to fetch customer data: ${response.status} - ${errorText}`);
				throw new Error(`Failed to fetch customer data: ${response.status} - ${errorText}`);
			}

			// Also refresh the available tags for the modal
			await loadCustomerTags();

			// Trigger refresh event to notify other components (like CustomerDetailSidebar)
			triggerRefresh();

		} catch (error) {
			console.error('Profile.svelte: refreshCustomerData(): Error refreshing customer data:', error);
			// Don't throw the error to prevent modal from staying open
		}
	}

	// Reactive statement to load tags when customer data is available
	$: if (customer && customer.customer_id && access_token) {
		loadCustomerTags();
	}

</script>

<!-- Customer Profile -->
<div class="mb-6 w-full">
	<div class="rounded-lg border border-gray-200 bg-white shadow-sm">
		<div class="border-b border-gray-200 p-4">
			<div class="flex items-center justify-between">
				<div class="flex items-center gap-2">
					<UserCircleSolid class="h-5 w-5 text-blue-600" />
					<h3 class="text-lg font-medium text-gray-700">{t('customer_sidebar_tab_overview_profile')}</h3>
				</div>
				<CustomerEdit {customer} onRefresh={refreshCustomerData} />
			</div>
		</div>
		<div class="p-4">
			<div class="grid grid-cols-3 gap-y-4">
				<div class="text-left text-sm text-gray-500">{t('customer_id')}</div>
				<div class="col-span-2 text-left text-sm font-medium">
					{customer.customer_id}
				</div>

				<div class="text-left text-sm text-gray-500">{t('first_name')}</div>
				<div class="col-span-2 text-left text-sm font-medium">
					{customer.first_name ? `${customer.first_name}` : t('not_provided')}
				</div>

				<div class="text-left text-sm text-gray-500">{t('last_name')}</div>
				<div class="col-span-2 text-left text-sm font-medium">
					{customer.last_name ? `${customer.last_name}` : t('not_provided')}
				</div>

				<div class="text-left text-sm text-gray-500">{t('date_of_birth')}</div>
				<div class="col-span-2 text-left text-sm font-medium">
					{customer.date_of_birth
						? formatDateOfBirth(customer.date_of_birth, lang)
						: t('not_provided')}
				</div>

				<div class="text-left text-sm text-gray-500">{t('national_id')}</div>
				<div class="col-span-2 text-left text-sm font-medium">
					{customer.national_id || t('not_provided')}
				</div>

				<div class="text-left text-sm text-gray-500">{t('nationality')}</div>
				<div class="col-span-2 text-left text-sm font-medium">
					{customer.nationality
						? getFullNationality(customer.nationality, lang)
						: t('not_provided')}
				</div>

				<div class="text-left text-sm text-gray-500">{t('passport_number')}</div>
				<div class="col-span-2 text-left text-sm font-medium">
					{customer.passport_number || t('not_provided')}
				</div>

				<div class="text-left text-sm text-gray-500">{t('address')}</div>
				<div class="col-span-2 text-left text-sm font-medium">
					{hasValidAddress(customer.address)
						? formatFullAddress(customer.address, lang)
						: t('not_provided')}
				</div>

				<div class="text-left text-sm text-gray-500">{t('phone_number')}</div>
				<div class="col-span-2 text-left text-sm font-medium">
					{customer.phone || t('not_provided')}
				</div>

				<div class="text-left text-sm text-gray-500">{t('email')}</div>
				<div class="col-span-2 text-left text-sm font-medium">
					{customer.email || t('not_provided')}
				</div>

				<div class="text-left text-sm text-gray-500">{t('career')}</div>
				<div class="col-span-2 text-left text-sm font-medium">
					{customer.career || t('not_provided')}
				</div>
			</div>
		</div>
	</div>
</div>

<!-- Customer Tags -->
<div class="mb-6 w-full">
	<div class="rounded-lg border border-gray-200 bg-white shadow-sm">
		<div class="border-b border-gray-200 p-4">
			<div class="flex items-center justify-between">
				<div class="flex items-center gap-2">
					<TagSolid class="h-5 w-5 text-blue-600" />
					<h3 class="text-lg font-medium text-gray-700">{t('customer_tags')}</h3>
					{#if refreshingTags}
						<div class="ml-2 h-4 w-4 animate-spin rounded-full border-b-2 border-blue-500"></div>
					{/if}
				</div>
				<CustomerTag {customer} customer_tags={customerTags} onRefresh={refreshCustomerTags} />
			</div>
		</div>
		<div class="p-4">
			{#if customer.tags && customer.tags.length > 0}
				<div class="flex flex-wrap gap-1">
					{#each customer.tags as tag}
						<span class="text-white-700 mb-1 mr-1 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm">
							<Indicator size="sm" class={`mr-1 ${getColorClass(tag.color || '#gray')} inline-block`} />
							{tag.name}
						</span>
					{/each}
				</div>
			{:else}
				<div class="text-center text-sm text-gray-500">
					{t('no_tags')}
				</div>
			{/if}
		</div>
	</div>
</div>
