<script lang="ts">
    import {
        Textarea,
        Button,
        Dropdown,
        DropdownItem
	} from 'flowbite-svelte';
    import {
        TrashBinOutline,
        PenOutline,
        PlusOutline,
		NewspaperSolid,
        AngleDownOutline
    } from 'flowbite-svelte-icons';
    import { t } from '$lib/stores/i18n';

    import { enhance } from '$app/forms';

    import NoteEditModal from '$src/routes/(site)/monitoring/[id]/InfoDisplayPanel/NoteEditModal.svelte';
    import NoteDeleteModal from '$src/routes/(site)/monitoring/[id]/InfoDisplayPanel/NoteDeleteModal.svelte';
    import { formatTimestamp } from '$lib/utils';
    import { services } from '$src/lib/api/features';

    let formEl: HTMLFormElement;          // ref to the form
    
    // State tracking for textarea content
    let noteContent = '';
    let initialNoteContent = '';

    // Reactive statements for button state management
    $: hasContent = noteContent.trim() !== '';
    $: hasChanges = noteContent !== initialNoteContent;
    $: canSubmit = hasContent || hasChanges;

    /** Submit on Enter (unless Shift is held) */
    function handleKeydown(e: KeyboardEvent) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();            // stop newline
            formEl?.requestSubmit();       // native submit → still goes through use:enhance
        }
    }
    export let customer: any;          // customer object
    export let customer_notes: any[];    // customer notes array
    export let access_token: string;      // access token for API calls

    // Note management variables
    let editModal = false;
    let deleteModal = false;
    let editNote;
    let deleteNoteId;
    
    // Loading states
    let loadingNotes = false;
    let notes: any[] = [];
    let refreshError: string | null = null;

    // Initialize notes with prop data
    $: if (customer_notes && notes.length === 0) {
        notes = [...customer_notes];
    }

    // Reactive loading when customer and access_token are available
    $: if (customer && customer.customer_id && access_token) {
        console.log('Reactive loading triggered for customer:', customer.customer_id);
        loadCustomerNotes(customer.customer_id);
    }

    // Helper function for retry logic
    async function retryWithDelay(fn: () => Promise<any>, maxRetries = 3, baseDelay = 200): Promise<any> {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return await fn();
            } catch (error) {
                console.log(`Attempt ${attempt} failed:`, error);
                if (attempt === maxRetries) {
                    throw error;
                }
                // Exponential backoff: 200ms, 400ms, 800ms
                const delay = baseDelay * Math.pow(2, attempt - 1);
                console.log(`Retrying in ${delay}ms...`);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
    }

    // Background data loading function with retry logic
    async function loadCustomerNotes(customerId: number) {
        try {
            loadingNotes = true;
            refreshError = null;
            console.log('Loading customer notes for customer ID:', customerId);
            
            // const response_customer_notes = await retryWithDelay(async () => {
            //     return await services.customers.getCustomerNotes(
            //         customerId,
            //         access_token
            //     );
            // });

			const response_customer_notes = await services.customers.getCustomerNotes(
				String(customerId),
				access_token
			);
            
            console.log('Received notes response:', response_customer_notes);
            notes = response_customer_notes.customer_notes || [];
            console.log('Updated notes array:', notes);
        } catch (error) {
            console.error('Error loading customer notes after retries:', error);
            refreshError = 'Failed to refresh notes. Please try again.';
            // Keep existing notes on error instead of clearing them
            // notes = [];
        } finally {
            loadingNotes = false;
        }
    }

    // Functions for note operations
    function openEditModal(note) {
        editModal = true;
        editNote = { ...note };
    }

    function closeEditModal() {
        editModal = false;
        editNote = null;
    }

    function openDeleteModal(id) {
        deleteModal = true;
        deleteNoteId = id;
    }

</script>

<!-- Customer Notes -->
<div class="mb-6 w-full">
	<div class="rounded-lg border border-gray-200 bg-white shadow-sm">
		<div class="border-b border-gray-200 p-4">
			<div class="flex items-center justify-between">
				<div class="flex items-center gap-2">
					<NewspaperSolid class="h-5 w-5 text-blue-600" />
					<h3 class="text-lg font-medium text-gray-700">{t('notes')}</h3>
				</div>
			</div>
		</div>
		<div class="p-4">
			<!-- Notes Form -->
			<form
				bind:this={formEl}
				method="POST"
				enctype="multipart/form-data"
				action="?/upload_note"
				use:enhance={() => {
					return async ({ update, result }) => {
						console.log(result.type);
						if (result.type === 'success') {
							// Reset textarea content and initial state
							noteContent = '';
							initialNoteContent = '';
							await update();            // refresh data
							// Add 300ms delay to ensure database transaction completes before fetching
							console.log('Form submission successful, waiting before reloading notes...');
							// await new Promise(resolve => setTimeout(resolve, 300));
							await loadCustomerNotes(customer.customer_id);
						}
					};
				}}
			>
				<!-- note + send -->
				<input type="hidden" name="customerId" value={customer.customer_id} />

				<div class="mb-3">
					<Textarea
						rows={2}
						name="note"
						placeholder={t('new_note')}
						required
						bind:value={noteContent}
						on:keydown={handleKeydown}     
						class="mb-2 p-4 bg-white rounded-lg border shadow-sm"
					/>

					<!-- still keeps mouse-friendly button -->
					<Button
						type="submit"
						color="blue"
						disabled={!canSubmit}
						class="disabled:cursor-not-allowed disabled:opacity-20 w-full"
					>
						<PlusOutline class="w-4 h-4 mr-2" />
						{t('add_note')}
					</Button>
				</div>
			</form>

			<!-- <Hr classHr="my-3" /> -->

			<!-- Error Display -->
			{#if refreshError}
				<div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
					<div class="text-red-700 text-sm">
						{refreshError}
						<button 
							on:click={() => loadCustomerNotes(customer.customer_id)}
							class="ml-2 underline hover:no-underline"
						>
							Retry
						</button>
					</div>
				</div>
			{/if}

			<!-- Customer Notes List -->
			<div class="space-y-4">
				{#if loadingNotes}
					<div class="text-center text-gray-500 p-4">
						{t('loading')}...
					</div>
				{:else if notes.length > 0}
					{#each notes as note (note.id)}
					<div id="info-tab-note-{note.id}" class="rounded-lg border border-gray-100 bg-white p-3 shadow-sm space-y-4" data-testid="note-item">
						<div class="text-md whitespace-pre-wrap leading-relaxed text-gray-900">
							{note.content}
						</div>
						<div class="flex items-center justify-between">
							<div class="flex flex-col space-y-1">
								<span class="text-xs text-gray-700">
									{t('note_created_on')}
									{formatTimestamp(note.created_on)}
									{t('note_created_by')}
									{note.created_by_name}
								</span>
								<span class="text-xs text-gray-700">
									{t('note_updated_on')}
									{formatTimestamp(note.updated_on)}
									{t('note_created_by')}
									{note.updated_by_name}
								</span>
							</div>

							<div>
								<Button
									color="light"
									class="flex h-6 w-6 items-center justify-center rounded-full p-2 text-gray-500"
								>
									<AngleDownOutline class="h-4 w-4" />
								</Button>

								<Dropdown>
									<DropdownItem
										id="info-tab-edit-note"
										class="flex items-center space-x-2"
										on:click={() => openEditModal(note)}
									>
										<PenOutline class="mr-2 h-4 w-4" />
										{t('edit')}
									</DropdownItem>
									<DropdownItem
										id="info-tab-delete-note"
										class="flex items-center space-x-2"
										on:click={() => openDeleteModal(note.id)}
									>
										<TrashBinOutline class="mr-2 h-4 w-4" />
										{t('delete')}
									</DropdownItem>
								</Dropdown>
							</div>
						</div>
					</div>
					{/each}
				{:else}
					<div class="text-center text-sm text-gray-500 p-4">
						{t('no_notes_available')}
					</div>
				{/if}
			</div>

			<!-- Note Edit Modal -->
			<NoteEditModal 
				bind:editModal 
				editNote={editNote} 
				customerId={customer.customer_id} 
				closeModal={closeEditModal}
				onSuccess={() => {
					// console.log('Note edit successful, waiting before reloading notes...');
					// await new Promise(resolve => setTimeout(resolve, 300));
					loadCustomerNotes(customer.customer_id);
				}}
			/>

			<!-- Note Delete Modal -->
			<NoteDeleteModal 
				bind:deleteModal 
				deleteNoteId={deleteNoteId} 
				customerId={customer.customer_id} 
				onSuccess={async () => {
					console.log('Note delete successful, waiting before reloading notes...');
					await new Promise(resolve => setTimeout(resolve, 300));
					loadCustomerNotes(customer.customer_id);
				}}
			/>
		</div>
	</div>
</div>