<script lang="ts">
    import { displayDate } from '$lib/utils';
    import { TrashBinOutline, BrainSolid } from 'flowbite-svelte-icons';
    import { enhance } from '$app/forms';
    import { t } from '$src/lib/stores/i18n';
    export let memories: any[];

    // reference for whichever memory-form is being clicked
    let deleteForm: HTMLFormElement;

    // optional: you can show a toast or something on success
    let showSuccess = false;
</script>

<!-- Customer Memory -->
<div class="mb-6 w-full">
	<div class="rounded-lg border border-gray-200 bg-white shadow-sm">
		<div class="border-b border-gray-200 p-4">
			<div class="flex items-center justify-between">
				<div class="flex items-center gap-2">
					<BrainSolid class="h-5 w-5 text-blue-600" />
					<h3 class="text-lg font-medium text-gray-700">{t('memories')}</h3>
				</div>
			</div>
		</div>
		<div class="p-4">
			{#if memories.length === 0}
				<div class="p-4 text-center text-sm text-gray-500">
					{t('no_memory')}
				</div>
			{:else}
				<div class="space-y-3">
					{#each [...memories].sort((a,b)=> new Date(b.created_on) - new Date(a.created_on)) as memory (memory.id)}
						<div class="bg-white rounded-lg shadow-sm border border-gray-100 p-3">
							<div class="grid grid-cols-[1fr_auto_auto] gap-4 items-start">
								<!-- description -->
								<div class="text-left text-gray-700">
									<div class="font-medium">{memory.detail_en}</div>
									<div class="text-sm text-gray-500">{memory.detail_th}</div>
								</div>

								<!-- timestamp -->
								<div class="text-sm text-gray-500 text-right w-32 self-start">
									<div>{displayDate(memory.created_on).date}</div>
									<div>{displayDate(memory.created_on).time}</div>
								</div>

								<!-- delete button -->
								<div class="text-right self-start">
									<form
										bind:this={deleteForm}
										action="?/delete_memory"
										method="POST"
										use:enhance={() => {
											return async ({ update, result }) => {
												if (result.success === true) {
													await update();
													showSuccess = true;
													window.location.reload();
												}
											}
										}}
									>
										<!-- send the memory ID -->
										<input type="hidden" name="memoryId" value={memory.id} />

										<!-- clicking this will submit via requestSubmit -->
										<button
											type="button"
											on:click={() => deleteForm.requestSubmit()}
											class="text-gray-400 hover:text-red-600 p-1 rounded-full"
											aria-label="Delete memory"
										>
											<TrashBinOutline class="h-4 w-4" />
										</button>
									</form>
								</div>
							</div>
						</div>
					{/each}
				</div>
			{/if}
		</div>
	</div>
</div>

{#if showSuccess}
	<div class="fixed bottom-4 right-4 bg-green-100 text-green-800 px-4 py-2 rounded">
		Memory deleted!
	</div>
{/if}
